# 解除复制限制 - Safari浏览器扩展

一个简单而强大的Safari扩展，用于移除网页上的复制粘贴限制，让您自由复制任何网页内容。

## 功能特性

### 🔓 核心功能
- **移除复制限制**: 自动解除网页上的 `oncopy`、`onpaste`、`oncut` 事件限制
- **恢复文本选择**: 移除 `user-select: none` CSS属性，恢复文本选择功能
- **启用右键菜单**: 解除右键菜单阻止 (`oncontextmenu`)
- **键盘快捷键**: 恢复 Ctrl+C、Ctrl+V、Cmd+C、Cmd+V 等快捷键功能
- **移除只读属性**: 自动移除输入框的 `readonly` 属性

### ⚙️ 高级功能
- **全局开关**: 一键启用/禁用所有网站的功能
- **按域名控制**: 为特定网站单独设置启用/禁用状态
- **域名管理**: 添加、移除、管理白名单/黑名单域名
- **实时反馈**: 页面加载时显示扩展状态提示
- **轻量设计**: 最小化性能影响，快速响应

## 安装方法

### 方法一：开发者模式安装
1. 打开Safari浏览器
2. 进入 `Safari` → `偏好设置` → `高级`
3. 勾选 `在菜单栏中显示"开发"菜单`
4. 在菜单栏点击 `开发` → `Web扩展` → `转换现有Web扩展...`
5. 选择本扩展的文件夹
6. 在Xcode中构建并运行

### 方法二：生成图标文件
1. 在浏览器中打开 `create_icons.html` 文件
2. 点击"生成图标"按钮
3. 下载所有尺寸的图标文件到 `icons/` 目录
4. 确保文件名为：`icon-16.png`、`icon-48.png`、`icon-128.png`

## 使用方法

### 基本使用
1. 安装扩展后，Safari工具栏会出现扩展图标
2. 访问任何网页，扩展将自动检测并解除复制限制
3. 看到绿色提示"✓ 复制限制已解除"表示功能已启用

### 弹出界面控制
点击扩展图标打开控制面板：

#### 主要控制
- **全局启用开关**: 控制扩展在所有网站的启用状态
- **当前网站控制**: 单独为当前访问的网站设置启用/禁用

#### 高级选项
- **域名管理**: 添加、移除特定域名的设置
- **显示通知**: 控制是否显示操作提示
- **新站点自动启用**: 设置访问新网站时的默认行为
- **重置设置**: 清除所有配置，恢复默认状态

## 技术实现

### 架构设计
- **Manifest V3**: 使用最新的WebExtensions API
- **内容脚本**: 在页面加载时注入，移除各种限制
- **后台脚本**: 处理存储、通信和状态管理
- **弹出界面**: 提供用户友好的控制面板

### 核心机制
1. **CSS覆盖**: 注入高优先级CSS样式覆盖限制性属性
2. **事件拦截**: 阻止和移除限制性JavaScript事件处理器
3. **DOM监听**: 实时监控新添加的元素并应用解除限制
4. **脚本注入**: 在页面级别阻止限制性事件监听器的添加

### 兼容性说明
- **Safari版本**: 支持Safari 15.4及以上版本
- **系统要求**: macOS 11.0+ / iOS 15.0+
- **Manifest**: 同时兼容Manifest V2和V3

## 文件结构

```
unlock_copy/
├── manifest.json              # 扩展配置文件
├── icons/                     # 扩展图标
│   ├── icon-16.png           # 16x16 图标
│   ├── icon-48.png           # 48x48 图标
│   ├── icon-128.png          # 128x128 图标
│   └── icon.svg              # SVG源图标
├── popup/                     # 弹出界面
│   ├── popup.html            # 界面结构
│   ├── popup.css             # 界面样式
│   └── popup.js              # 界面逻辑
├── content/                   # 内容脚本
│   ├── content.js            # 主功能脚本
│   └── override.css          # CSS覆盖样式
├── background/               # 后台脚本
│   └── background.js         # 状态管理和通信
├── create_icons.html         # 图标生成工具
└── README.md                 # 说明文档
```

## 常见问题

### Q: 为什么有些网站仍然无法复制？
A: 某些网站可能使用更复杂的限制机制。请尝试刷新页面，或在扩展设置中为该域名单独启用功能。

### Q: 扩展会影响网页正常功能吗？
A: 扩展经过优化设计，不会影响网页的正常交互功能，如按钮点击、表单提交等。

### Q: 如何查看扩展是否在某个网站上工作？
A: 查看扩展图标颜色（绿色=启用，灰色=禁用）或页面右上角的状态提示。

### Q: 可以为特定网站禁用扩展吗？
A: 可以。点击扩展图标，在弹出面板中为当前网站单独设置启用/禁用状态。

## 隐私保护

本扩展严格保护用户隐私：
- ✅ 不收集任何个人信息
- ✅ 不上传任何浏览数据
- ✅ 所有设置仅存储在本地
- ✅ 开源代码，可审查安全性

## 许可证

MIT License - 详见LICENSE文件

## 贡献

欢迎提交问题报告和功能建议！

---

**注意**: 此扩展仅用于合法的个人使用场景，请遵守网站的使用条款并尊重内容版权。