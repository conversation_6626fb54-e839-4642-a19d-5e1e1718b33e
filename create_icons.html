<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标生成器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .icon-size {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .icon {
            margin-bottom: 10px;
        }
        canvas {
            border: 1px solid #ddd;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Safari扩展图标生成器</h1>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <ol>
                <li>点击"生成图标"按钮生成不同尺寸的图标</li>
                <li>右键点击每个图标，选择"图片另存为"</li>
                <li>保存为对应的文件名：icon-16.png, icon-48.png, icon-128.png</li>
                <li>将图标文件放到 icons/ 目录下</li>
            </ol>
        </div>
        
        <button onclick="generateIcons()">生成图标</button>
        <button onclick="downloadAll()">下载所有图标</button>
        
        <div class="icon-preview" id="iconPreview">
            <!-- 图标将在这里生成 -->
        </div>
    </div>

    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const scale = size / 128;
            
            // 清空画布
            ctx.clearRect(0, 0, size, size);
            
            // 创建渐变
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#4CAF50');
            gradient.addColorStop(1, '#2E7D32');
            
            // 绘制背景圆形
            ctx.beginPath();
            ctx.arc(size/2, size/2, 60*scale, 0, 2 * Math.PI);
            ctx.fillStyle = gradient;
            ctx.fill();
            ctx.strokeStyle = '#1B5E20';
            ctx.lineWidth = 2*scale;
            ctx.stroke();
            
            // 绘制复制图标
            ctx.save();
            ctx.translate(32*scale, 32*scale);
            
            // 第一个文档
            ctx.fillStyle = 'white';
            ctx.strokeStyle = '#2E7D32';
            ctx.lineWidth = 2*scale;
            ctx.fillRect(8*scale, 8*scale, 32*scale, 40*scale);
            ctx.strokeRect(8*scale, 8*scale, 32*scale, 40*scale);
            
            // 文档内容线条
            ctx.fillStyle = '#2E7D32';
            ctx.fillRect(12*scale, 16*scale, 24*scale, 2*scale);
            ctx.fillRect(12*scale, 22*scale, 20*scale, 2*scale);
            ctx.fillRect(12*scale, 28*scale, 18*scale, 2*scale);
            ctx.fillRect(12*scale, 34*scale, 16*scale, 2*scale);
            
            // 第二个文档（复制的）
            ctx.fillStyle = 'white';
            ctx.fillRect(16*scale, 16*scale, 32*scale, 40*scale);
            ctx.strokeRect(16*scale, 16*scale, 32*scale, 40*scale);
            
            // 第二个文档内容线条
            ctx.fillStyle = '#2E7D32';
            ctx.fillRect(20*scale, 24*scale, 24*scale, 2*scale);
            ctx.fillRect(20*scale, 30*scale, 20*scale, 2*scale);
            ctx.fillRect(20*scale, 36*scale, 18*scale, 2*scale);
            ctx.fillRect(20*scale, 42*scale, 16*scale, 2*scale);
            
            ctx.restore();
            
            // 绘制解锁图标
            ctx.save();
            ctx.translate(88*scale, 88*scale);
            
            // 黄色圆形背景
            ctx.beginPath();
            ctx.arc(0, 0, 18*scale, 0, 2 * Math.PI);
            ctx.fillStyle = '#FFC107';
            ctx.fill();
            ctx.strokeStyle = '#FF8F00';
            ctx.lineWidth = 2*scale;
            ctx.stroke();
            
            // X标记
            ctx.strokeStyle = '#FF8F00';
            ctx.lineWidth = 3*scale;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(-8*scale, -8*scale);
            ctx.lineTo(8*scale, 8*scale);
            ctx.moveTo(8*scale, -8*scale);
            ctx.lineTo(-8*scale, 8*scale);
            ctx.stroke();
            
            // 中心点
            ctx.beginPath();
            ctx.arc(0, 0, 4*scale, 0, 2 * Math.PI);
            ctx.fillStyle = 'white';
            ctx.fill();
            
            ctx.restore();
        }
        
        function generateIcons() {
            const sizes = [16, 48, 128];
            const preview = document.getElementById('iconPreview');
            preview.innerHTML = '';
            
            sizes.forEach(size => {
                const div = document.createElement('div');
                div.className = 'icon-size';
                
                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                canvas.className = 'icon';
                
                drawIcon(canvas, size);
                
                const label = document.createElement('div');
                label.textContent = `${size}x${size}`;
                
                const downloadBtn = document.createElement('button');
                downloadBtn.textContent = '下载';
                downloadBtn.onclick = () => downloadIcon(canvas, `icon-${size}.png`);
                
                div.appendChild(canvas);
                div.appendChild(label);
                div.appendChild(downloadBtn);
                preview.appendChild(div);
            });
        }
        
        function downloadIcon(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function downloadAll() {
            const canvases = document.querySelectorAll('canvas');
            const sizes = [16, 48, 128];
            
            canvases.forEach((canvas, index) => {
                setTimeout(() => {
                    downloadIcon(canvas, `icon-${sizes[index]}.png`);
                }, index * 100);
            });
        }
        
        // 页面加载时自动生成图标
        window.onload = generateIcons;
    </script>
</body>
</html>