<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="64" cy="64" r="60" fill="url(#gradient)" stroke="#1B5E20" stroke-width="2"/>
  
  <!-- 复制图标 -->
  <g transform="translate(32, 32)">
    <!-- 第一个文档 -->
    <rect x="8" y="8" width="32" height="40" rx="3" fill="white" stroke="#2E7D32" stroke-width="2"/>
    <rect x="12" y="16" width="24" height="2" fill="#2E7D32"/>
    <rect x="12" y="22" width="20" height="2" fill="#2E7D32"/>
    <rect x="12" y="28" width="18" height="2" fill="#2E7D32"/>
    <rect x="12" y="34" width="16" height="2" fill="#2E7D32"/>
    
    <!-- 第二个文档（复制的） -->
    <rect x="16" y="16" width="32" height="40" rx="3" fill="white" stroke="#2E7D32" stroke-width="2"/>
    <rect x="20" y="24" width="24" height="2" fill="#2E7D32"/>
    <rect x="20" y="30" width="20" height="2" fill="#2E7D32"/>
    <rect x="20" y="36" width="18" height="2" fill="#2E7D32"/>
    <rect x="20" y="42" width="16" height="2" fill="#2E7D32"/>
  </g>
  
  <!-- 解锁图标 -->
  <g transform="translate(88, 88)">
    <circle cx="0" cy="0" r="18" fill="#FFC107" stroke="#FF8F00" stroke-width="2"/>
    <path d="M-8,-8 L8,8 M8,-8 L-8,8" stroke="#FF8F00" stroke-width="3" stroke-linecap="round"/>
    <circle cx="0" cy="0" r="4" fill="white"/>
  </g>
</svg>