# 安装指南

## 快速开始

### 第一步：生成图标文件
1. 在浏览器中打开 `create_icons.html` 文件
2. 点击"生成图标"按钮
3. 下载所有图标文件到 `icons/` 目录：
   - `icon-16.png`
   - `icon-48.png` 
   - `icon-128.png`

### 第二步：在Safari中安装扩展

#### 方法A：开发者模式（推荐）
1. 打开Safari浏览器
2. 菜单栏选择 `Safari` → `偏好设置`
3. 点击 `高级` 标签页
4. 勾选 `在菜单栏中显示"开发"菜单`
5. 菜单栏点击 `开发` → `Web扩展` → `转换现有Web扩展...`
6. 选择 `unlock_copy` 文件夹
7. 在打开的Xcode中点击运行按钮

#### 方法B：使用safari-web-extension-converter
```bash
# 安装转换工具
xcrun safari-web-extension-converter unlock_copy

# 在Xcode中构建和运行
```

### 第三步：启用扩展
1. 在Safari中，选择 `Safari` → `偏好设置`  
2. 点击 `扩展` 标签页
3. 找到 "解除复制限制" 并勾选启用
4. 工具栏会出现扩展图标

## 功能验证

访问任意有复制限制的网站，例如：
- 某些新闻网站
- 学术文献网站  
- 付费内容网站

应该能看到：
- 页面右上角显示 "✓ 复制限制已解除"
- 可以正常选择和复制文本
- 右键菜单正常工作
- 键盘快捷键 Cmd+C/Cmd+V 可用

## 故障排除

### 扩展图标不显示
- 确保在Safari偏好设置中启用了扩展
- 重启Safari浏览器
- 检查扩展是否在Xcode中成功构建

### 功能不生效
- 点击扩展图标，检查是否已启用
- 刷新网页重新加载扩展
- 查看浏览器控制台是否有错误信息

### 图标显示异常
- 确保所有尺寸的PNG图标文件都已正确生成
- 检查图标文件路径和命名是否正确

## 卸载方法

1. Safari偏好设置 → 扩展 → 取消勾选扩展
2. 删除扩展文件夹
3. 在应用程序中删除生成的扩展应用（如果有）

---

如遇到问题，请参考 README.md 中的常见问题部分。