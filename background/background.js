chrome.runtime.onInstalled.addListener(() => {
    chrome.storage.sync.set({
        enabled: true,
        domainSettings: {},
        showNotifications: true
    });
    
    console.log('解除复制限制扩展已安装');
});

chrome.action.onClicked.addListener((tab) => {
    chrome.tabs.sendMessage(tab.id, {action: 'toggle'}, (response) => {
        if (chrome.runtime.lastError) {
            console.log('无法与内容脚本通信:', chrome.runtime.lastError.message);
        }
    });
});

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'updateIcon') {
        const iconPath = request.enabled ? 'icons/icon-128.png' : 'icons/icon-128-disabled.png';
        chrome.action.setIcon({
            path: iconPath,
            tabId: sender.tab.id
        });
    }
    
    if (request.action === 'getDomainSettings') {
        chrome.storage.sync.get(['domainSettings'], (result) => {
            sendResponse(result.domainSettings || {});
        });
        return true;
    }
    
    if (request.action === 'setDomainSetting') {
        chrome.storage.sync.get(['domainSettings'], (result) => {
            const settings = result.domainSettings || {};
            settings[request.domain] = request.enabled;
            chrome.storage.sync.set({domainSettings: settings}, () => {
                sendResponse({success: true});
            });
        });
        return true;
    }
    
    if (request.action === 'getGlobalSetting') {
        chrome.storage.sync.get(['enabled'], (result) => {
            sendResponse({enabled: result.enabled !== false});
        });
        return true;
    }
    
    if (request.action === 'setGlobalSetting') {
        chrome.storage.sync.set({enabled: request.enabled}, () => {
            chrome.tabs.query({}, (tabs) => {
                tabs.forEach(tab => {
                    chrome.tabs.sendMessage(tab.id, {
                        action: 'toggle',
                        enabled: request.enabled
                    }, () => {
                        if (chrome.runtime.lastError) {
                        }
                    });
                });
            });
            sendResponse({success: true});
        });
        return true;
    }
});

chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
        const url = new URL(tab.url);
        const hostname = url.hostname;
        
        chrome.storage.sync.get(['enabled', 'domainSettings'], (result) => {
            let isEnabled = result.enabled !== false;
            
            if (result.domainSettings && result.domainSettings[hostname] !== undefined) {
                isEnabled = result.domainSettings[hostname];
            }
            
            const iconPath = isEnabled ? 'icons/icon-128.png' : 'icons/icon-128-disabled.png';
            chrome.action.setIcon({
                path: iconPath,
                tabId: tabId
            });
        });
    }
});

chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'sync') {
        if (changes.enabled || changes.domainSettings) {
            chrome.tabs.query({}, (tabs) => {
                tabs.forEach(tab => {
                    if (tab.url && (tab.url.startsWith('http://') || tab.url.startsWith('https://'))) {
                        chrome.tabs.sendMessage(tab.id, {
                            action: 'settingsChanged'
                        }, () => {
                            if (chrome.runtime.lastError) {
                            }
                        });
                    }
                });
            });
        }
    }
});