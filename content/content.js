(function() {
    'use strict';
    
    let isEnabled = true;
    let originalHandlers = new Map();
    
    function log(message) {
        console.log('[解除复制限制]', message);
    }
    
    function removeEventHandlers() {
        const events = ['copy', 'cut', 'paste', 'selectstart', 'contextmenu', 'dragstart'];
        const elements = document.querySelectorAll('*');
        
        events.forEach(eventType => {
            elements.forEach(element => {
                const handlerProp = 'on' + eventType;
                if (element[handlerProp]) {
                    if (!originalHandlers.has(element)) {
                        originalHandlers.set(element, {});
                    }
                    originalHandlers.get(element)[eventType] = element[handlerProp];
                    element[handlerProp] = null;
                }
                
                if (element.hasAttribute(handlerProp)) {
                    element.removeAttribute(handlerProp);
                }
            });
            
            document.addEventListener(eventType, function(e) {
                e.stopPropagation();
                e.stopImmediatePropagation();
            }, true);
        });
    }
    
    function removeStyleRestrictions() {
        const style = document.createElement('style');
        style.id = 'unlock-copy-restrictions';
        style.textContent = `
            * {
                -webkit-user-select: text !important;
                -moz-user-select: text !important;
                -ms-user-select: text !important;
                user-select: text !important;
                -webkit-touch-callout: default !important;
                -khtml-user-select: text !important;
                -webkit-user-drag: none !important;
            }
            
            /* 只允许媒体元素和明确标记为可拖拽的元素被拖拽 */
            img, 
            video, 
            audio, 
            canvas, 
            svg,
            [draggable="true"] {
                -webkit-user-drag: element !important;
                pointer-events: auto !important;
            }
            
            /* 确保输入框正常工作 */
            input, 
            textarea, 
            [contenteditable] {
                -webkit-user-drag: none !important;
            }
        `;
        
        if (document.head) {
            document.head.appendChild(style);
        } else {
            document.addEventListener('DOMContentLoaded', () => {
                document.head.appendChild(style);
            });
        }
    }
    
    function enableKeyboardShortcuts() {
        const shortcuts = ['c', 'v', 'x', 'a', 's', 'p'];
        
        document.addEventListener('keydown', function(e) {
            if ((e.ctrlKey || e.metaKey) && shortcuts.includes(e.key.toLowerCase())) {
                e.stopPropagation();
                e.stopImmediatePropagation();
            }
        }, true);
    }
    
    function injectScriptToRemoveProtection() {
        const script = document.createElement('script');
        script.textContent = `
            (function() {
                const originalAddEventListener = EventTarget.prototype.addEventListener;
                EventTarget.prototype.addEventListener = function(type, listener, options) {
                    if (['copy', 'cut', 'paste', 'selectstart', 'contextmenu', 'dragstart'].includes(type)) {
                        return;
                    }
                    return originalAddEventListener.call(this, type, listener, options);
                };
                
                Object.defineProperty(document, 'oncopy', {
                    set: function() {},
                    get: function() { return null; }
                });
                Object.defineProperty(document, 'oncut', {
                    set: function() {},
                    get: function() { return null; }
                });
                Object.defineProperty(document, 'onpaste', {
                    set: function() {},
                    get: function() { return null; }
                });
                Object.defineProperty(document, 'onselectstart', {
                    set: function() {},
                    get: function() { return null; }
                });
                Object.defineProperty(document, 'oncontextmenu', {
                    set: function() {},
                    get: function() { return null; }
                });
                
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1) {
                                ['oncopy', 'oncut', 'onpaste', 'onselectstart', 'oncontextmenu', 'ondragstart'].forEach(attr => {
                                    if (node[attr]) {
                                        node[attr] = null;
                                    }
                                    if (node.hasAttribute(attr)) {
                                        node.removeAttribute(attr);
                                    }
                                });
                            }
                        });
                    });
                });
                
                observer.observe(document.body || document.documentElement, {
                    childList: true,
                    subtree: true
                });
            })();
        `;
        
        (document.head || document.documentElement).appendChild(script);
        script.remove();
    }
    
    function removeReadonlyAttributes() {
        const inputs = document.querySelectorAll('input[readonly], textarea[readonly]');
        inputs.forEach(input => {
            input.removeAttribute('readonly');
        });
        
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) {
                        if (node.matches && node.matches('input[readonly], textarea[readonly]')) {
                            node.removeAttribute('readonly');
                        }
                        const readonlyElements = node.querySelectorAll && node.querySelectorAll('input[readonly], textarea[readonly]');
                        if (readonlyElements) {
                            readonlyElements.forEach(el => el.removeAttribute('readonly'));
                        }
                    }
                });
            });
        });
        
        observer.observe(document.body || document.documentElement, {
            childList: true,
            subtree: true
        });
    }
    
    function initExtension() {
        chrome.storage.sync.get(['enabled', 'domainSettings'], function(result) {
            const hostname = window.location.hostname;
            isEnabled = result.enabled !== false;
            
            if (result.domainSettings && result.domainSettings[hostname] !== undefined) {
                isEnabled = result.domainSettings[hostname];
            }
            
            if (isEnabled) {
                applyUnlockFeatures();
                log('已启用，复制粘贴限制已解除');
            } else {
                log('已禁用');
            }
        });
    }
    
    function applyUnlockFeatures() {
        removeEventHandlers();
        removeStyleRestrictions();
        enableKeyboardShortcuts();
        injectScriptToRemoveProtection();
        removeReadonlyAttributes();
        
        const style = document.createElement('style');
        style.textContent = `
            body::after {
                content: "✓ 复制限制已解除";
                position: fixed;
                top: 10px;
                right: 10px;
                background: #4CAF50;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
                z-index: 999999;
                animation: fadeInOut 3s ease-in-out;
                pointer-events: none;
            }
            
            @keyframes fadeInOut {
                0%, 100% { opacity: 0; }
                10%, 90% { opacity: 1; }
            }
        `;
        document.head.appendChild(style);
        
        setTimeout(() => {
            if (style.parentNode) {
                style.parentNode.removeChild(style);
            }
        }, 3000);
    }
    
    chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
        if (request.action === 'toggle') {
            isEnabled = request.enabled;
            if (isEnabled) {
                applyUnlockFeatures();
            } else {
                location.reload();
            }
            sendResponse({success: true});
        }
    });
    
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initExtension);
    } else {
        initExtension();
    }
})();