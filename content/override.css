/* 解除复制粘贴限制的CSS覆盖 */

* {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
    -webkit-touch-callout: default !important;
    -khtml-user-select: text !important;
}

/* 确保输入框可以正常选择 */
input, textarea, [contenteditable] {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
}

/* 禁止文本元素被拖拽，确保可以正常选择文本 */
* {
    -webkit-user-drag: none !important;
    pointer-events: auto !important;
}

/* 允许特定媒体元素拖拽 */
img, 
video, 
audio, 
canvas, 
svg,
[draggable="true"] {
    -webkit-user-drag: element !important;
    pointer-events: auto !important;
}

/* 确保输入框有正确的拖拽行为 */
input, 
textarea, 
[contenteditable] {
    -webkit-user-drag: none !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
}

/* 确保链接和按钮可以选择文本但不会被整体拖拽 */
a, 
button {
    -webkit-user-drag: none !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
}

/* 特殊处理一些常见的阻止选择的类名 */
.no-select,
.noselect,
.unselectable,
[unselectable="on"] {
    -webkit-user-drag: none !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
}

/* 确保所有元素都可以被选择 */
*[style*="user-select"] {
    -webkit-user-drag: none !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
}