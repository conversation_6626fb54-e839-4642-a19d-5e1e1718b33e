document.addEventListener('DOMContentLoaded', function() {
    const elements = {
        globalToggle: document.getElementById('globalToggle'),
        siteToggle: document.getElementById('siteToggle'),
        siteToggleText: document.getElementById('siteToggleText'),
        statusDot: document.getElementById('statusDot'),
        statusText: document.getElementById('statusText'),
        currentDomain: document.getElementById('currentDomain'),
        siteStatus: document.getElementById('siteStatus'),
        advancedToggle: document.getElementById('advancedToggle'),
        advancedPanel: document.getElementById('advancedPanel'),
        domainList: document.getElementById('domainList'),
        domainInput: document.getElementById('domainInput'),
        addDomain: document.getElementById('addDomain'),
        resetSettings: document.getElementById('resetSettings'),
        enabledSites: document.getElementById('enabledSites'),
        showNotifications: document.getElementById('showNotifications'),
        autoEnable: document.getElementById('autoEnable')
    };
    
    let currentTab = null;
    let currentDomain = null;
    let globalEnabled = true;
    let domainSettings = {};
    
    async function getCurrentTab() {
        const tabs = await chrome.tabs.query({active: true, currentWindow: true});
        return tabs[0];
    }
    
    function extractDomain(url) {
        try {
            return new URL(url).hostname;
        } catch {
            return null;
        }
    }
    
    function updateUI() {
        elements.globalToggle.checked = globalEnabled;
        
        if (currentDomain) {
            elements.currentDomain.textContent = currentDomain;
            const domainEnabled = domainSettings[currentDomain] !== undefined 
                ? domainSettings[currentDomain] 
                : globalEnabled;
            
            const finalEnabled = globalEnabled && domainEnabled;
            
            elements.statusDot.className = `status-dot ${finalEnabled ? 'enabled' : 'disabled'}`;
            elements.statusText.textContent = finalEnabled ? '已启用' : '已禁用';
            elements.siteStatus.textContent = finalEnabled ? '复制限制已解除' : '复制限制未解除';
            
            elements.siteToggle.className = `btn btn-primary ${domainEnabled ? '' : 'disabled'}`;
            elements.siteToggleText.textContent = domainEnabled ? '禁用此站点' : '启用此站点';
        } else {
            elements.currentDomain.textContent = '无效页面';
            elements.statusDot.className = 'status-dot disabled';
            elements.statusText.textContent = '不支持此页面';
            elements.siteStatus.textContent = '扩展不可用';
            elements.siteToggle.disabled = true;
        }
        
        updateDomainList();
        updateStats();
    }
    
    function updateDomainList() {
        elements.domainList.innerHTML = '';
        
        Object.entries(domainSettings).forEach(([domain, enabled]) => {
            const item = document.createElement('div');
            item.className = 'domain-item';
            
            item.innerHTML = `
                <span class="domain-name">${domain}</span>
                <div class="domain-actions">
                    <button class="btn btn-small domain-toggle ${enabled ? 'btn-primary' : 'btn-secondary'}">
                        ${enabled ? '启用' : '禁用'}
                    </button>
                    <button class="domain-remove" title="移除">✕</button>
                </div>
            `;
            
            const toggleBtn = item.querySelector('.domain-toggle');
            const removeBtn = item.querySelector('.domain-remove');
            
            toggleBtn.addEventListener('click', () => toggleDomain(domain));
            removeBtn.addEventListener('click', () => removeDomain(domain));
            
            elements.domainList.appendChild(item);
        });
    }
    
    function updateStats() {
        const enabledCount = Object.values(domainSettings).filter(enabled => enabled).length;
        elements.enabledSites.textContent = `已启用: ${enabledCount} 个站点`;
    }
    
    async function toggleGlobal() {
        globalEnabled = !globalEnabled;
        await chrome.runtime.sendMessage({
            action: 'setGlobalSetting',
            enabled: globalEnabled
        });
        updateUI();
    }
    
    async function toggleCurrentSite() {
        if (!currentDomain) return;
        
        const currentSetting = domainSettings[currentDomain] !== undefined 
            ? domainSettings[currentDomain] 
            : globalEnabled;
        
        const newSetting = !currentSetting;
        
        await chrome.runtime.sendMessage({
            action: 'setDomainSetting',
            domain: currentDomain,
            enabled: newSetting
        });
        
        domainSettings[currentDomain] = newSetting;
        updateUI();
    }
    
    async function toggleDomain(domain) {
        const newSetting = !domainSettings[domain];
        
        await chrome.runtime.sendMessage({
            action: 'setDomainSetting',
            domain: domain,
            enabled: newSetting
        });
        
        domainSettings[domain] = newSetting;
        updateUI();
    }
    
    async function removeDomain(domain) {
        delete domainSettings[domain];
        
        await chrome.runtime.sendMessage({
            action: 'setDomainSetting',
            domain: domain,
            enabled: undefined
        });
        
        updateUI();
    }
    
    async function addDomain() {
        const domain = elements.domainInput.value.trim().toLowerCase();
        if (!domain) return;
        
        if (domain.includes('/') || domain.includes(':')) {
            alert('请输入有效的域名，例如: example.com');
            return;
        }
        
        domainSettings[domain] = true;
        
        await chrome.runtime.sendMessage({
            action: 'setDomainSetting',
            domain: domain,
            enabled: true
        });
        
        elements.domainInput.value = '';
        updateUI();
    }
    
    async function resetSettings() {
        if (!confirm('确定要重置所有设置吗？这将清除所有域名配置。')) {
            return;
        }
        
        await chrome.storage.sync.clear();
        await chrome.storage.sync.set({
            enabled: true,
            domainSettings: {},
            showNotifications: true,
            autoEnable: false
        });
        
        await loadSettings();
        updateUI();
    }
    
    function toggleAdvanced() {
        const panel = elements.advancedPanel;
        const arrow = elements.advancedToggle.querySelector('.arrow');
        
        if (panel.classList.contains('hidden')) {
            panel.classList.remove('hidden');
            arrow.classList.add('rotated');
        } else {
            panel.classList.add('hidden');
            arrow.classList.remove('rotated');
        }
    }
    
    async function loadSettings() {
        const result = await chrome.storage.sync.get([
            'enabled', 
            'domainSettings', 
            'showNotifications', 
            'autoEnable'
        ]);
        
        globalEnabled = result.enabled !== false;
        domainSettings = result.domainSettings || {};
        elements.showNotifications.checked = result.showNotifications !== false;
        elements.autoEnable.checked = result.autoEnable === true;
    }
    
    async function init() {
        await loadSettings();
        
        currentTab = await getCurrentTab();
        if (currentTab && currentTab.url) {
            currentDomain = extractDomain(currentTab.url);
        }
        
        updateUI();
    }
    
    elements.globalToggle.addEventListener('change', toggleGlobal);
    elements.siteToggle.addEventListener('click', toggleCurrentSite);
    elements.advancedToggle.addEventListener('click', toggleAdvanced);
    elements.addDomain.addEventListener('click', addDomain);
    elements.resetSettings.addEventListener('click', resetSettings);
    
    elements.domainInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            addDomain();
        }
    });
    
    elements.showNotifications.addEventListener('change', (e) => {
        chrome.storage.sync.set({showNotifications: e.target.checked});
    });
    
    elements.autoEnable.addEventListener('change', (e) => {
        chrome.storage.sync.set({autoEnable: e.target.checked});
    });
    
    init();
});