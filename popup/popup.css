* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 360px;
    min-height: 400px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background: #f8f9fa;
}

.container {
    padding: 16px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e9ecef;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.version {
    font-size: 12px;
    color: #6c757d;
    background: #e9ecef;
    padding: 2px 8px;
    border-radius: 12px;
}

.status-section {
    margin-bottom: 20px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #6c757d;
    transition: background-color 0.3s ease;
}

.status-dot.enabled {
    background: #28a745;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
}

.status-dot.disabled {
    background: #dc3545;
}

.controls {
    margin-bottom: 20px;
}

.control-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    margin-bottom: 12px;
}

.control-label {
    font-weight: 500;
}

.switch {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #007bff;
}

input:checked + .slider:before {
    transform: translateX(24px);
}

.current-site {
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    padding: 16px;
}

.site-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.site-icon {
    font-size: 20px;
}

.site-details {
    flex: 1;
}

.domain {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 2px;
}

.site-status {
    font-size: 12px;
    color: #6c757d;
}

.site-controls {
    display: flex;
    justify-content: flex-end;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0066cc;
}

.btn-primary.disabled {
    background: #dc3545;
}

.btn-primary.disabled:hover {
    background: #c82333;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    width: 100%;
    justify-content: space-between;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.advanced-section {
    margin-bottom: 20px;
}

.advanced-panel {
    margin-top: 12px;
    padding: 16px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.advanced-panel.hidden {
    display: none;
}

.domain-management h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #2c3e50;
}

.domain-list {
    max-height: 120px;
    overflow-y: auto;
    margin-bottom: 12px;
}

.domain-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
}

.domain-item:last-child {
    border-bottom: none;
}

.domain-name {
    font-size: 13px;
    color: #2c3e50;
}

.domain-actions {
    display: flex;
    gap: 4px;
}

.domain-toggle {
    font-size: 11px;
    padding: 4px 8px;
}

.domain-remove {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
}

.domain-remove:hover {
    background: #f8d7da;
}

.add-domain {
    display: flex;
    gap: 8px;
}

.add-domain input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 13px;
}

.add-domain input:focus {
    outline: none;
    border-color: #007bff;
}

.options {
    margin: 16px 0;
    padding-top: 16px;
    border-top: 1px solid #f1f3f4;
}

.option-group {
    margin-bottom: 8px;
}

.option-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    cursor: pointer;
}

.option-label input[type="checkbox"] {
    margin: 0;
}

.actions {
    padding-top: 16px;
    border-top: 1px solid #f1f3f4;
}

.footer {
    padding-top: 16px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stats {
    font-size: 12px;
    color: #6c757d;
}

.links {
    display: flex;
    gap: 12px;
}

.links a {
    font-size: 12px;
    color: #007bff;
    text-decoration: none;
}

.links a:hover {
    text-decoration: underline;
}

.arrow {
    transition: transform 0.3s ease;
}

.arrow.rotated {
    transform: rotate(180deg);
}

::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}